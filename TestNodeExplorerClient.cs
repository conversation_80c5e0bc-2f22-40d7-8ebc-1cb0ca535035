using System;
using System.Threading.Tasks;
using GamesEngine.Business.Liquidity.ExternalServices;

namespace TestNodeExplorer
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Testing NodeExplorerClient with public APIs...");
            
            try
            {
                // Create client with dummy URL (won't be used for public APIs)
                var client = new NodeExplorerClient("http://dummy-url");
                
                // Test UTXO retrieval with the XPUB from the code
                const string XPUB = "tpubDDnkuEUFyDKpk5QMsaFhohhbi2M6NauretBpcQSSchKN5H55iZc4tvi4GA8C8TCc9CPnpvfwC8U7pudc2H5DN91P7kyERt2akF1U5gdB2QQ";
                
                Console.WriteLine("Testing UTXO retrieval...");
                var utxoResponse = await client.GetUTXOsAsync(XPUB);
                
                Console.WriteLine($"Confirmed UTXOs: {utxoResponse.Confirmed?.Utxos?.Count ?? 0}");
                Console.WriteLine($"Unconfirmed UTXOs: {utxoResponse.Unconfirmed?.Utxos?.Count ?? 0}");
                Console.WriteLine($"Total Balance: {utxoResponse.Confirmed?.TotalBalance ?? "0"}");
                
                if (utxoResponse.Confirmed?.Utxos?.Count > 0)
                {
                    var firstUtxo = utxoResponse.Confirmed.Utxos[0];
                    Console.WriteLine($"First UTXO: {firstUtxo.TransactionId}:{firstUtxo.Index} - {firstUtxo.Value} BTC");
                }
                
                // Test fee rate retrieval
                Console.WriteLine("\nTesting fee rate retrieval...");
                var feeRate = await client.GetFeeRateAsync();
                Console.WriteLine($"Fee rate: {feeRate.SatoshiPerByte} sat/byte");
                
                // Test address balance (derive first address from XPUB)
                Console.WriteLine("\nTesting address balance...");
                // This would require deriving an address from the XPUB
                // For now, let's use a known testnet address
                var testAddress = "tb1qw508d6qejxtdg4y5r3zarvary0c5xw7kxpjzsx";
                var balance = await client.AddressBalanceAsync(testAddress, "BTC");
                Console.WriteLine($"Address balance: {balance} BTC");
                
                Console.WriteLine("\nAll tests completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
    }
}

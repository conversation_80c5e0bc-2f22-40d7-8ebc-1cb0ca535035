using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GamesEngine.Business.Liquidity.ExternalServices;
using NBitcoin;

namespace TestPSBTFix
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Testing PSBT generation fix for WitnessProgramMissmatch error...");
            
            try
            {
                // Initialize the client
                var client = new NodeExplorerClient("https://blockstream.info/testnet/api", Network.TestNet, "BTC");
                
                // Test recipients for multi-output PSBT
                var recipients = new List<(string address, Money amount)>
                {
                    ("tb1qw508d6qejxtdg4y5r3zarvary0c5xw7kxpjzsx", Money.Satoshis(10000)), // 0.0001 BTC
                    ("tb1qrp33g0q5c5txsp9arysrx4k6zdkfs4nce4xj0gdcccefvpysxf3q0sL5k7", Money.Satoshis(5000))  // 0.00005 BTC
                };
                
                Console.WriteLine("Testing GenerateMultiOutputPSBTAsync...");
                Console.WriteLine($"Recipients: {recipients.Count}");
                foreach (var recipient in recipients)
                {
                    Console.WriteLine($"  - {recipient.address}: {recipient.amount} sats");
                }
                
                // This should now work without the WitnessProgramMissmatch error
                var psbt = await client.GenerateMultiOutputPSBTAsync(recipients);
                
                Console.WriteLine($"✅ PSBT generated successfully!");
                Console.WriteLine($"PSBT has {psbt.Inputs.Count} inputs and {psbt.Outputs.Count} outputs");
                
                // Check that witness UTXOs are properly set
                int witnessUtxoCount = 0;
                for (int i = 0; i < psbt.Inputs.Count; i++)
                {
                    var input = psbt.Inputs[i];
                    if (input.WitnessUtxo != null)
                    {
                        witnessUtxoCount++;
                        Console.WriteLine($"  Input {i}: WitnessUtxo set with value {input.WitnessUtxo.Value} sats");
                    }
                    else
                    {
                        Console.WriteLine($"  Input {i}: No WitnessUtxo (this might cause issues)");
                    }
                }
                
                Console.WriteLine($"✅ {witnessUtxoCount}/{psbt.Inputs.Count} inputs have WitnessUtxo set");
                
                // Test single output PSBT as well
                Console.WriteLine("\nTesting GeneratePSBTAsync...");
                var singlePsbt = await client.GeneratePSBTAsync("tb1qw508d6qejxtdg4y5r3zarvary0c5xw7kxpjzsx", Money.Satoshis(15000));
                
                Console.WriteLine($"✅ Single output PSBT generated successfully!");
                Console.WriteLine($"PSBT has {singlePsbt.Inputs.Count} inputs and {singlePsbt.Outputs.Count} outputs");
                
                // Check witness UTXOs for single PSBT
                witnessUtxoCount = 0;
                for (int i = 0; i < singlePsbt.Inputs.Count; i++)
                {
                    var input = singlePsbt.Inputs[i];
                    if (input.WitnessUtxo != null)
                    {
                        witnessUtxoCount++;
                    }
                }
                
                Console.WriteLine($"✅ {witnessUtxoCount}/{singlePsbt.Inputs.Count} inputs have WitnessUtxo set");
                
                Console.WriteLine("\n🎉 All tests passed! The WitnessProgramMissmatch error should be fixed.");
                Console.WriteLine("The PSBT inputs now have proper WitnessUtxo information for SegWit transactions.");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error during testing: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                
                if (ex.Message.Contains("WitnessProgramMissmatch"))
                {
                    Console.WriteLine("\n⚠️  The WitnessProgramMissmatch error still occurs.");
                    Console.WriteLine("This means the fix may need additional adjustments.");
                }
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}

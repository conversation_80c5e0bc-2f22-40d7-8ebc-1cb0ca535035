using System;
using System.Threading.Tasks;
using GamesEngine.Business.Liquidity.ExternalServices;
using NBitcoin;

namespace TestNodeExplorer
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Testing NodeExplorerClient with Blockstream API...");
            
            try
            {
                // Initialize the client
                var client = new NodeExplorerClient("https://blockstream.info/testnet/api", Network.TestNet, "BTC");
                
                // Test descriptor (this is a testnet xpub)
                string testDescriptor = "wpkh(tpubDDnkuEUFyDKpk5QMsaFhohhbi2M6NauretBpcQSSchKN5H55iZc4tvi4GA8C8TCc9CPnpvfwC8U7pudc2H5DN91P7kyERt2akF1U5gdB2QQ/0/*)";
                
                Console.WriteLine($"Testing with descriptor: {testDescriptor}");
                
                // Test UTXO retrieval
                Console.WriteLine("\n1. Testing UTXO retrieval...");
                var utxos = await client.GetUTXOsAsync(testDescriptor);
                Console.WriteLine($"Found {utxos.UTXOs.Count} UTXOs");
                
                foreach (var utxo in utxos.UTXOs)
                {
                    Console.WriteLine($"  - UTXO: {utxo.Outpoint.Hash}:{utxo.Outpoint.N} - {utxo.Value} sats");
                }
                
                // Test fee rate estimation
                Console.WriteLine("\n2. Testing fee rate estimation...");
                var feeRate = await client.GetFeeRateAsync(6);
                Console.WriteLine($"Estimated fee rate: {feeRate.SatoshiPerByte} sat/vB");
                
                // Test address balance (using a known testnet address)
                Console.WriteLine("\n3. Testing address balance...");
                string testAddress = "tb1qw508d6qejxtdg4y5r3zarvary0c5xw7kxpjzsx"; // Example testnet address
                var balance = await client.AddressBalanceInfoAsync(testAddress);
                Console.WriteLine($"Address balance: {balance.Balance} sats");
                
                Console.WriteLine("\nAll tests completed successfully!");
                Console.WriteLine("The NodeExplorerClient is now using Blockstream's public API instead of local NBXplorer.");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during testing: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}

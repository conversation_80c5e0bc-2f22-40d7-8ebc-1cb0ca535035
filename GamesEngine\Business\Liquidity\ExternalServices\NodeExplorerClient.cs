using System;
using System.Net.Http;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Text.Json;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System.Linq;
using System.Net;
using NBitcoin;
using NBitcoin.DataEncoders;
using NBitcoin.RPC;
using Newtonsoft.Json.Linq;
using System.IO;
using System.Collections.Generic;

namespace GamesEngine.Business.Liquidity.ExternalServices
{
    public class NodeExplorerClient : INodeExplorerClient
    {
        private readonly HttpClient _httpClient;
        private readonly string _explorerBaseUrl;
        private const decimal SatsPerBtc = 100_000_000m;
        private readonly Network _network;
        private readonly string _cryptoCode;
        const string XPUB = "tpubDDnkuEUFyDKpk5QMsaFhohhbi2M6NauretBpcQSSchKN5H55iZc4tvi4GA8C8TCc9CPnpvfwC8U7pudc2H5DN91P7kyERt2akF1U5gdB2QQ";
        private readonly HDFingerprint _masterFingerprint = new HDFingerprint(Encoders.Hex.DecodeData("fc015d69"));

        public NodeExplorerClient(HttpClient httpClient)
        {
            _network = Network.TestNet;
            _cryptoCode = "BTC"; // Node Explorer uses "BTC" for both mainnet and testnet
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _explorerBaseUrl = _httpClient.BaseAddress?.ToString() ?? throw new ArgumentException("HttpClient must have a BaseAddress configured.");
        }

        public NodeExplorerClient(string baseUrl)
        {
            if (string.IsNullOrEmpty(baseUrl)) throw new ArgumentNullException(nameof(baseUrl));

            _network = Network.TestNet;
            _cryptoCode = "BTC"; // Node Explorer uses "BTC" for both mainnet and testnet
            _httpClient = new HttpClient();
            _httpClient.BaseAddress = new Uri(baseUrl);
            _explorerBaseUrl = baseUrl;
        }

        public async Task<NodeExplorerBalanceInfo> AddressBalanceInfoAsync(string address, string currencyCode)
        {
            if (string.IsNullOrEmpty(address)) throw new ArgumentNullException(nameof(address));
            if (string.IsNullOrEmpty(currencyCode)) throw new ArgumentNullException(nameof(currencyCode));

            try
            {
                // Use Blockstream public API for address balance
                var blockstreamBaseUrl = "https://blockstream.info/testnet/api";
                var requestUri = $"{blockstreamBaseUrl}/address/{address}";

                using var blockstreamClient = new HttpClient();
                blockstreamClient.Timeout = TimeSpan.FromSeconds(30);

                var response = await blockstreamClient.GetAsync(requestUri);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    var ex = new Exception($"Failed requesting address balance from Blockstream API at {requestUri}. Response status: {response.StatusCode}, Body: {responseBody}");
                    Loggers.GetIntance().NodeExplorer.Error(ex.Message, ex);
                    ErrorsSender.Send(ex, $"{nameof(AddressBalanceInfoAsync)} balance retrieval", ex.Message);
                    throw ex;
                }

                var blockstreamAddress = JsonSerializer.Deserialize<BlockstreamAddressInfo>(responseBody);

                // Convert Blockstream format to NBXplorer format
                return new NodeExplorerBalanceInfo
                {
                    Confirmed = blockstreamAddress.ChainStats.FundedTxoSum - blockstreamAddress.ChainStats.SpentTxoSum,
                    Unconfirmed = blockstreamAddress.MempoolStats.FundedTxoSum - blockstreamAddress.MempoolStats.SpentTxoSum,
                    Available = (blockstreamAddress.ChainStats.FundedTxoSum - blockstreamAddress.ChainStats.SpentTxoSum) +
                               (blockstreamAddress.MempoolStats.FundedTxoSum - blockstreamAddress.MempoolStats.SpentTxoSum),
                    Total = (blockstreamAddress.ChainStats.FundedTxoSum - blockstreamAddress.ChainStats.SpentTxoSum) +
                           (blockstreamAddress.MempoolStats.FundedTxoSum - blockstreamAddress.MempoolStats.SpentTxoSum)
                };
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error fetching address balance for {address}: {ex.Message}";
                Loggers.GetIntance().NodeExplorer.Error(errorMessage, ex);
                ErrorsSender.Send(ex, $"{nameof(AddressBalanceInfoAsync)} balance retrieval", errorMessage);
                throw;
            }
        }

        public async Task<decimal> AddressBalanceAsync(string address, string currencyCode)
        {
            var balanceInfo = await AddressBalanceInfoAsync(address, currencyCode);

            long availableSatoshis = balanceInfo?.Available ?? 0;

            decimal balanceInBtc = availableSatoshis / SatsPerBtc;
            return balanceInBtc;
        }

        public async Task<NodeUtxoResponse> GetUTXOsAsync(string descriptor)
        {
            try
            {
                // Use Blockstream public API instead of local NBXplorer
                var blockstreamBaseUrl = "https://blockstream.info/testnet/api";

                // Parse the descriptor to get addresses
                var addresses = await GetAddressesFromDescriptor(descriptor);

                var allUtxos = new List<NodeUnspentCoin>();

                // Fetch UTXOs for each address using Blockstream API
                foreach (var addressInfo in addresses)
                {
                    try
                    {
                        var requestUri = $"{blockstreamBaseUrl}/address/{addressInfo.Address}/utxo";

                        using var blockstreamClient = new HttpClient();
                        blockstreamClient.Timeout = TimeSpan.FromSeconds(30);

                        var response = await blockstreamClient.GetAsync(requestUri);
                        var responseBody = await response.Content.ReadAsStringAsync();

                        if (!response.IsSuccessStatusCode)
                        {
                            Loggers.GetIntance().NodeExplorer.Debug($"Failed to get UTXOs for address {addressInfo.Address}: {response.StatusCode}");
                            continue; // Skip this address and continue with others
                        }

                        var blockstreamUtxos = JsonSerializer.Deserialize<BlockstreamUtxo[]>(responseBody);

                        // Convert Blockstream format to NBXplorer format
                        foreach (var utxo in blockstreamUtxos)
                        {
                            var nodeUtxo = new NodeUnspentCoin
                            {
                                TransactionId = utxo.Txid,
                                Index = utxo.Vout,
                                Value = (utxo.Value / 100_000_000m).ToString("0.########"), // Convert satoshis to BTC
                                ScriptPubKeyHex = await GetScriptPubKeyForAddress(addressInfo.Address),
                                KeyPath = addressInfo.KeyPath
                            };
                            allUtxos.Add(nodeUtxo);
                        }
                    }
                    catch (Exception ex)
                    {
                        Loggers.GetIntance().NodeExplorer.Debug($"Error fetching UTXOs for address {addressInfo.Address}: {ex.Message}");
                        // Continue with other addresses
                    }
                }

                // Return in NBXplorer format
                return new NodeUtxoResponse
                {
                    Confirmed = new UtxoSet
                    {
                        Utxos = allUtxos,
                        TotalBalance = allUtxos.Sum(u => decimal.Parse(u.Value)).ToString("0.########")
                    },
                    Unconfirmed = new UtxoSet
                    {
                        Utxos = new List<NodeUnspentCoin>(),
                        TotalBalance = "0"
                    }
                };
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error fetching UTXOs for descriptor {descriptor}: {ex.Message}";
                Loggers.GetIntance().NodeExplorer.Error(errorMessage, ex);
                ErrorsSender.Send(ex, $"{nameof(GetUTXOsAsync)} UTXO retrieval", errorMessage);
                throw;
            }
        }

        public async Task<FeeRate> GetFeeRateAsync()
        {
            try
            {
                // Use Blockstream public API for fee estimates
                var blockstreamBaseUrl = "https://blockstream.info/testnet/api";
                var requestUri = $"{blockstreamBaseUrl}/fee-estimates";

                using var blockstreamClient = new HttpClient();
                blockstreamClient.Timeout = TimeSpan.FromSeconds(30);

                var response = await blockstreamClient.GetAsync(requestUri);
                var responseBody = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    // Fallback to a reasonable default fee rate for testnet
                    Loggers.GetIntance().NodeExplorer.Debug($"Failed to get fee rate from Blockstream API, using default: {response.StatusCode}");
                    return new FeeRate(Money.Satoshis(1000)); // 1 sat/vB default
                }

                var jsonDoc = JsonDocument.Parse(responseBody);

                // Try to get 6-block confirmation fee rate, fallback to others
                decimal satPerVb = 1; // Default fallback

                if (jsonDoc.RootElement.TryGetProperty("6", out var sixBlock))
                {
                    satPerVb = sixBlock.GetDecimal();
                }
                else if (jsonDoc.RootElement.TryGetProperty("3", out var threeBlock))
                {
                    satPerVb = threeBlock.GetDecimal();
                }
                else if (jsonDoc.RootElement.TryGetProperty("1", out var oneBlock))
                {
                    satPerVb = oneBlock.GetDecimal();
                }

                // Convert sat/vB to sat/KB (multiply by 1000)
                var satPerKb = satPerVb * 1000;
                return new FeeRate(Money.Satoshis(satPerKb));
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error fetching fee rate: {ex.Message}";
                Loggers.GetIntance().NodeExplorer.Error(errorMessage, ex);

                // Return a reasonable default fee rate instead of throwing
                return new FeeRate(Money.Satoshis(1000)); // 1 sat/vB default
            }
        }

        public async Task<BroadcastResult> BroadcastTxAsync(Transaction tx)
        {
            try
            {
                // Use Blockstream public API for transaction broadcasting
                var blockstreamBaseUrl = "https://blockstream.info/testnet/api";
                var requestUri = $"{blockstreamBaseUrl}/tx";

                using var blockstreamClient = new HttpClient();
                blockstreamClient.Timeout = TimeSpan.FromSeconds(30);

                var payload = new StringContent(tx.ToHex(), Encoding.UTF8, "text/plain");
                var response = await blockstreamClient.PostAsync(requestUri, payload);

                if (response.IsSuccessStatusCode)
                {
                    var txid = await response.Content.ReadAsStringAsync();
                    Loggers.GetIntance().NodeExplorer.Debug($"Transaction broadcast successful. TXID: {txid}");
                    return new BroadcastResult { Success = true };
                }
                else
                {
                    var errorBody = await response.Content.ReadAsStringAsync();
                    var ex = new Exception($"Failed to broadcast transaction via Blockstream API. Status: {response.StatusCode}, Body: {errorBody}");
                    Loggers.GetIntance().NodeExplorer.Error(ex.Message, ex);
                    ErrorsSender.Send(ex, $"{nameof(BroadcastTxAsync)} transaction broadcast", ex.Message);
                    return new BroadcastResult { Success = false };
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error broadcasting transaction: {ex.Message}";
                Loggers.GetIntance().NodeExplorer.Error(errorMessage, ex);
                ErrorsSender.Send(ex, $"{nameof(BroadcastTxAsync)} transaction broadcast", errorMessage);
                return new BroadcastResult { Success = false };
            }
        }

        public async Task<PSBT> GeneratePSBTAsync(string recipientAddressStr, Money amountToSend)
        {
            try
            {
                var recipientAddress = BitcoinAddress.Create(recipientAddressStr, _network);

                var extPubKey = ExtPubKey.Parse(XPUB, _network);
                var receiveDescriptor = XPUB;

                var utxoResponse = await GetUTXOsAsync(receiveDescriptor);
                var confirmedUtxos = utxoResponse.Confirmed?.Utxos ?? new List<NodeUnspentCoin>();
                var unconfirmedUtxos = utxoResponse.Unconfirmed?.Utxos ?? new List<NodeUnspentCoin>();
                var utxos = confirmedUtxos.Concat(unconfirmedUtxos).ToList();
                var feeRate = await GetFeeRateAsync();

                if (!utxos.Any()) throw new InvalidOperationException("No UTXOs found for this xpub. Cannot create transaction.");

                var builder = _network.CreateTransactionBuilder();
                var coins = utxos.Select(u => u.AsCoin()).ToList();
                builder.AddCoins(coins);
                builder.Send(recipientAddress, amountToSend);
                builder.SendEstimatedFees(feeRate);

                var changeKey = extPubKey.Derive(1).Derive(0);
                var changeScriptPubKey = changeKey.PubKey.WitHash.ScriptPubKey;
                builder.SetChange(changeScriptPubKey);

                var tx = builder.BuildTransaction(sign: false);
                var spentCoins = builder.FindSpentCoins(tx);
                var totalInput = spentCoins.Sum(c => ((Money)c.Amount).Satoshi);
                var totalOutput = tx.Outputs.Sum(o => o.Value.Satoshi);
                var fee = Money.Satoshis(totalInput - totalOutput);

                if (!builder.Verify(tx, out var errors))
                {
                    var errorMessage = string.Join(Environment.NewLine, errors.Select(e => e.ToString()));
                    throw new InvalidOperationException($"Could not build transaction. Errors: {errorMessage}");
                }

                var psbt = PSBT.FromTransaction(tx, _network);
                psbt.AddCoins(spentCoins);

                var utxoDict = utxos.ToDictionary(u => u.OutPoint);
                for (var i = 0; i < psbt.Inputs.Count; i++)
                {
                    var input = psbt.Inputs[i];
                    if (utxoDict.TryGetValue(input.PrevOut, out var utxo))
                    {
                        try
                        {
                            // Set witness UTXO for SegWit transactions
                            var witnessUtxo = new TxOut(utxo.Amount, utxo.ScriptPubKey);
                            input.WitnessUtxo = witnessUtxo;

                            var keyPathParts = utxo.KeyPath.Split('/');
                            if (keyPathParts.Length >= 2 && uint.TryParse(keyPathParts[1], out var addressIndex))
                            {
                                var derivedKey = extPubKey.Derive(0).Derive(addressIndex);
                                var keyPath = new KeyPath($"0/{addressIndex}");
                                var rootedKeyPath = new RootedKeyPath(_masterFingerprint, keyPath);
                                input.HDKeyPaths.Add(derivedKey.PubKey, rootedKeyPath);
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Warning: Could not add HD key path for input {i}: {ex.Message}");
                        }
                    }
                }

                var changeOutput = tx.Outputs.FirstOrDefault(o => o.ScriptPubKey == changeScriptPubKey);
                if (changeOutput != null)
                {
                    var changeIndex = tx.Outputs.IndexOf(changeOutput);
                    var changeKeyPath = new KeyPath("1/0");
                    var rootedKeyPath = new RootedKeyPath(_masterFingerprint, changeKeyPath);
                    psbt.Outputs[changeIndex].HDKeyPaths.Add(changeKey.PubKey, rootedKeyPath);
                }

                return psbt;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Failed to generate PSBT: {ex.Message}";
                Loggers.GetIntance().NodeExplorer.Error(errorMessage, ex);
                ErrorsSender.Send(ex, $"{nameof(GeneratePSBTAsync)} PSBT generation", errorMessage);
                throw;
            }
        }

        public async Task<PSBT> GenerateMultiOutputPSBTAsync(List<(string address, Money amount)> recipients)
        {
            try
            {
                if (recipients == null || recipients.Count == 0) throw new ArgumentException("Recipients list cannot be null or empty");

                var extPubKey = ExtPubKey.Parse(XPUB, _network);
                var receiveDescriptor = $"wpkh({XPUB}/0/*)";

                var utxoResponse = await GetUTXOsAsync(receiveDescriptor);
                var confirmedUtxos = utxoResponse.Confirmed?.Utxos ?? new List<NodeUnspentCoin>();
                var unconfirmedUtxos = utxoResponse.Unconfirmed?.Utxos ?? new List<NodeUnspentCoin>();
                var utxos = confirmedUtxos.Concat(unconfirmedUtxos).ToList();
                var feeRate = await GetFeeRateAsync();

                if (!utxos.Any()) throw new InvalidOperationException("No UTXOs found for this xpub. Cannot create transaction.");

                var builder = _network.CreateTransactionBuilder();
                var coins = utxos.Select(u => u.AsCoin()).ToList();
                builder.AddCoins(coins);

                foreach (var recipient in recipients)
                {
                    var recipientAddress = BitcoinAddress.Create(recipient.address, _network);
                    builder.Send(recipientAddress, recipient.amount);
                }

                builder.SendEstimatedFees(feeRate);

                var changeKey = extPubKey.Derive(1).Derive(0);
                var changeScriptPubKey = changeKey.PubKey.WitHash.ScriptPubKey;
                builder.SetChange(changeScriptPubKey);

                var tx = builder.BuildTransaction(sign: false);
                var spentCoins = builder.FindSpentCoins(tx);
                var totalInput = spentCoins.Sum(c => ((Money)c.Amount).Satoshi);
                var totalOutput = tx.Outputs.Sum(o => o.Value.Satoshi);
                var fee = Money.Satoshis(totalInput - totalOutput);

                if (!builder.Verify(tx, out var errors))
                {
                    var errorMessage = string.Join(Environment.NewLine, errors.Select(e => e.ToString()));
                    throw new InvalidOperationException($"Could not build transaction. Errors: {errorMessage}");
                }

                var psbt = PSBT.FromTransaction(tx, _network);
                psbt.AddCoins(spentCoins);

                var utxoDict = utxos.ToDictionary(u => u.OutPoint);
                for (var i = 0; i < psbt.Inputs.Count; i++)
                {
                    var input = psbt.Inputs[i];
                    if (utxoDict.TryGetValue(input.PrevOut, out var utxo))
                    {
                        try
                        {
                            // Set witness UTXO for SegWit transactions
                            var witnessUtxo = new TxOut(utxo.Amount, utxo.ScriptPubKey);
                            input.WitnessUtxo = witnessUtxo;

                            var keyPathParts = utxo.KeyPath.Split('/');
                            if (keyPathParts.Length >= 2 && uint.TryParse(keyPathParts[1], out var addressIndex))
                            {
                                var derivedKey = extPubKey.Derive(0).Derive(addressIndex);
                                var keyPath = new KeyPath($"0/{addressIndex}");
                                var rootedKeyPath = new RootedKeyPath(_masterFingerprint, keyPath);
                                input.HDKeyPaths.Add(derivedKey.PubKey, rootedKeyPath);
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Warning: Could not add HD key path for input {i}: {ex.Message}");
                        }
                    }
                }

                var changeOutput = tx.Outputs.FirstOrDefault(o => o.ScriptPubKey == changeScriptPubKey);
                if (changeOutput != null)
                {
                    var changeIndex = tx.Outputs.IndexOf(changeOutput);
                    var changeKeyPath = new KeyPath("1/0");
                    var rootedKeyPath = new RootedKeyPath(_masterFingerprint, changeKeyPath);
                    psbt.Outputs[changeIndex].HDKeyPaths.Add(changeKey.PubKey, rootedKeyPath);
                }

                return psbt;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Failed to generate multi-output PSBT: {ex.Message}";
                Loggers.GetIntance().NodeExplorer.Error(errorMessage, ex);
                ErrorsSender.Send(ex, $"{nameof(GenerateMultiOutputPSBTAsync)} PSBT generation", errorMessage);
                throw;
            }
        }

        public static void SavePsbtToFile(PSBT psbt, string filePath)
        {
            File.WriteAllBytes(filePath, psbt.ToBytes());
        }

        private async Task<List<AddressInfo>> GetAddressesFromDescriptor(string descriptor)
        {
            var addresses = new List<AddressInfo>();

            try
            {
                // Handle different descriptor formats
                if (descriptor.StartsWith("wpkh(") && descriptor.Contains("/0/*"))
                {
                    // Extract xpub from wpkh descriptor
                    var xpubStart = descriptor.IndexOf("(") + 1;
                    var xpubEnd = descriptor.IndexOf("/0/*");
                    var xpub = descriptor.Substring(xpubStart, xpubEnd - xpubStart);

                    var extPubKey = ExtPubKey.Parse(xpub, _network);

                    // Generate first 20 receive addresses (0/0 to 0/19)
                    for (uint i = 0; i < 20; i++)
                    {
                        var derivedKey = extPubKey.Derive(0).Derive(i);
                        var address = derivedKey.PubKey.WitHash.GetAddress(_network);
                        addresses.Add(new AddressInfo
                        {
                            Address = address.ToString(),
                            KeyPath = $"0/{i}"
                        });
                    }
                }
                else if (descriptor.Contains("tpub") && !descriptor.Contains("wpkh"))
                {
                    // Direct xpub - assume it's for receive addresses
                    var extPubKey = ExtPubKey.Parse(descriptor, _network);

                    // Generate first 20 receive addresses (0/0 to 0/19)
                    for (uint i = 0; i < 20; i++)
                    {
                        var derivedKey = extPubKey.Derive(0).Derive(i);
                        var address = derivedKey.PubKey.WitHash.GetAddress(_network);
                        addresses.Add(new AddressInfo
                        {
                            Address = address.ToString(),
                            KeyPath = $"0/{i}"
                        });
                    }
                }
                else
                {
                    // Assume it's a single address
                    addresses.Add(new AddressInfo
                    {
                        Address = descriptor,
                        KeyPath = "0/0"
                    });
                }
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().NodeExplorer.Debug($"Error parsing descriptor {descriptor}: {ex.Message}");
                // Fallback: treat as single address
                addresses.Add(new AddressInfo
                {
                    Address = descriptor,
                    KeyPath = "0/0"
                });
            }

            return addresses;
        }

        private async Task<string> GetScriptPubKeyForAddress(string address)
        {
            try
            {
                var bitcoinAddress = BitcoinAddress.Create(address, _network);
                return bitcoinAddress.ScriptPubKey.ToHex();
            }
            catch
            {
                // Fallback for unknown address formats
                return "";
            }
        }
    }

    public class BroadcastResult
    {
        public bool Success { get; set; }
    }

    public class NodeExplorerBalanceInfo
    {
        [JsonPropertyName("confirmed")]
        public long Confirmed { get; set; }

        [JsonPropertyName("unconfirmed")]
        public long Unconfirmed { get; set; }

        [JsonPropertyName("available")]
        public long Available { get; set; }

        [JsonPropertyName("total")]
        public long Total { get; set; }
    }
    public class NodeUtxoResponse
    {
        [JsonPropertyName("confirmed")] public UtxoSet Confirmed { get; set; }
        [JsonPropertyName("unconfirmed")] public UtxoSet Unconfirmed { get; set; }
    }

    public class UtxoSet
    {
        [JsonPropertyName("utxos")] public List<NodeUnspentCoin> Utxos { get; set; }
        [JsonPropertyName("totalBalance")] public string TotalBalance { get; set; }
    }

    public class NodeUnspentCoin
    {
        [JsonPropertyName("keyPath")] public string KeyPath { get; set; }
        [JsonPropertyName("transactionId")] public string TransactionId { get; set; }
        [JsonPropertyName("index")] public uint Index { get; set; }
        [JsonPropertyName("value")] public string Value { get; set; }
        [JsonPropertyName("scriptPubKey")] public string ScriptPubKeyHex { get; set; }

        [JsonIgnore] public OutPoint OutPoint => new OutPoint(uint256.Parse(TransactionId), Index);
        [JsonIgnore] public Money Amount => Money.Parse(Value);
        [JsonIgnore] public NBitcoin.Script ScriptPubKey => NBitcoin.Script.FromHex(ScriptPubKeyHex);

        public Coin AsCoin() => new Coin(OutPoint, new TxOut(Amount, ScriptPubKey));
    }

    // Blockstream API UTXO model
    public class BlockstreamUtxo
    {
        [JsonPropertyName("txid")]
        public string Txid { get; set; }

        [JsonPropertyName("vout")]
        public uint Vout { get; set; }

        [JsonPropertyName("value")]
        public long Value { get; set; } // Value in satoshis

        [JsonPropertyName("status")]
        public BlockstreamUtxoStatus Status { get; set; }
    }

    public class BlockstreamUtxoStatus
    {
        [JsonPropertyName("confirmed")]
        public bool Confirmed { get; set; }

        [JsonPropertyName("block_height")]
        public int? BlockHeight { get; set; }

        [JsonPropertyName("block_hash")]
        public string BlockHash { get; set; }

        [JsonPropertyName("block_time")]
        public long? BlockTime { get; set; }
    }

    // Blockstream API address info model
    public class BlockstreamAddressInfo
    {
        [JsonPropertyName("address")]
        public string Address { get; set; }

        [JsonPropertyName("chain_stats")]
        public BlockstreamAddressStats ChainStats { get; set; }

        [JsonPropertyName("mempool_stats")]
        public BlockstreamAddressStats MempoolStats { get; set; }
    }

    public class BlockstreamAddressStats
    {
        [JsonPropertyName("funded_txo_count")]
        public int FundedTxoCount { get; set; }

        [JsonPropertyName("funded_txo_sum")]
        public long FundedTxoSum { get; set; }

        [JsonPropertyName("spent_txo_count")]
        public int SpentTxoCount { get; set; }

        [JsonPropertyName("spent_txo_sum")]
        public long SpentTxoSum { get; set; }

        [JsonPropertyName("tx_count")]
        public int TxCount { get; set; }
    }

    // Helper class for address derivation
    public class AddressInfo
    {
        public string Address { get; set; }
        public string KeyPath { get; set; }
    }
}

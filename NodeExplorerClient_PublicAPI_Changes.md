# NodeExplorerClient Public API Migration

## Overview
The `NodeExplorerClient` has been modified to use public Bitcoin APIs instead of the local NBXplorer instance that was returning zero UTXOs. The client now uses Blockstream's public API endpoints for Bitcoin testnet operations.

## Changes Made

### 1. UTXO Retrieval (`GetUTXOsAsync`)
- **Before**: Used local NBXplorer API endpoint: `{_explorerBaseUrl}v1/cryptos/{_cryptoCode}/derivations/{encodedDescriptor}/utxos`
- **After**: Uses Blockstream API: `https://blockstream.info/testnet/api/address/{address}/utxo`

**Key Changes**:
- Added descriptor parsing to derive addresses from xpub/wpkh descriptors
- Generates first 20 receive addresses (0/0 to 0/19) from the descriptor
- Fetches UTXOs for each derived address individually
- Converts Blockstream UTXO format to NBXplorer format for compatibility
- Added error handling to continue with other addresses if one fails

### 2. Fee Rate Retrieval (`GetFeeRateAsync`)
- **Before**: Used local NBXplorer API: `{_explorerBaseUrl}v1/cryptos/{_cryptoCode}/fees/6`
- **After**: Uses Blockstream API: `https://blockstream.info/testnet/api/fee-estimates`

**Key Changes**:
- Fetches fee estimates from Blockstream API
- Prefers 6-block confirmation rate, falls back to 3-block or 1-block
- Converts sat/vB to sat/KB (multiply by 1000)
- Returns reasonable default (1 sat/vB) if API fails instead of throwing

### 3. Transaction Broadcasting (`BroadcastTxAsync`)
- **Before**: Used local NBXplorer API: `{_explorerBaseUrl}/v1/cryptos/{_cryptoCode}/transactions`
- **After**: Uses Blockstream API: `https://blockstream.info/testnet/api/tx`

**Key Changes**:
- Sends raw transaction hex as plain text instead of JSON
- Returns success/failure status instead of throwing on failure
- Logs transaction ID on successful broadcast

### 4. Address Balance (`AddressBalanceInfoAsync`)
- **Before**: Used local NBXplorer API: `{_explorerBaseUrl}/v1/cryptos/{currencyCode}/addresses/{address}/balance`
- **After**: Uses Blockstream API: `https://blockstream.info/testnet/api/address/{address}`

**Key Changes**:
- Fetches comprehensive address information from Blockstream
- Calculates confirmed/unconfirmed balances from chain_stats and mempool_stats
- Converts satoshi values to maintain compatibility with existing code

## New Models Added

### BlockstreamUtxo
```csharp
public class BlockstreamUtxo
{
    public string Txid { get; set; }
    public uint Vout { get; set; }
    public long Value { get; set; } // Value in satoshis
    public BlockstreamUtxoStatus Status { get; set; }
}
```

### BlockstreamAddressInfo
```csharp
public class BlockstreamAddressInfo
{
    public string Address { get; set; }
    public BlockstreamAddressStats ChainStats { get; set; }
    public BlockstreamAddressStats MempoolStats { get; set; }
}
```

### AddressInfo (Helper)
```csharp
public class AddressInfo
{
    public string Address { get; set; }
    public string KeyPath { get; set; }
}
```

## Helper Methods Added

### `GetAddressesFromDescriptor(string descriptor)`
- Parses different descriptor formats (wpkh, direct xpub)
- Derives Bitcoin addresses from extended public keys
- Generates key paths for proper PSBT signing
- Handles error cases gracefully

### `GetScriptPubKeyForAddress(string address)`
- Converts Bitcoin address to script public key hex
- Used for UTXO compatibility with existing coin selection logic

## Benefits

1. **Reliability**: No longer dependent on local NBXplorer instance
2. **Public Access**: Uses publicly available Blockstream API
3. **Compatibility**: Maintains existing interface and data structures
4. **Error Handling**: Graceful degradation when individual addresses fail
5. **Testnet Support**: Specifically configured for Bitcoin testnet

## Configuration

The client still accepts the same constructor parameters, but the base URL is now only used for backward compatibility. All Bitcoin operations use the hardcoded Blockstream testnet API endpoints:

- Base URL: `https://blockstream.info/testnet/api`
- Network: Bitcoin Testnet
- Timeout: 30 seconds per request

## Testing

A test file `TestNodeExplorerClient.cs` has been created to verify:
- UTXO retrieval from xpub descriptors
- Fee rate estimation
- Address balance queries
- Error handling

## Notes

- The client generates the first 20 addresses from any xpub descriptor
- UTXOs are fetched individually per address (may be slower but more reliable)
- Fee rates default to 1 sat/vB if the API is unavailable
- Transaction broadcasting returns success/failure instead of throwing exceptions
- All existing method signatures and return types remain unchanged for compatibility
